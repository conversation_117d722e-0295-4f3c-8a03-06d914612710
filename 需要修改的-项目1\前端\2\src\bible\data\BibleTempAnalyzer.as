package bible.data
{
   import road7th.data.DictionaryData;
   
   public class BibleTempAnalyzer
   {
      
      public static function analysis(_arg_1:XML) : DictionaryData
      {
         var _local_4:XML = null;
         var _local_3:BibleTempInfo = null;
         var _local_2:DictionaryData = new DictionaryData();
         for each(_local_4 in _arg_1.Item)
         {
            _local_3 = new BibleTempInfo();
            _local_3.MainType = int(_local_4.@MainType);
            _local_3.SonsType = int(_local_4.@SonsType);
            _local_3.ItemType = int(_local_4.@ItemType);
            _local_3.ItemName = String(_local_4.@ItemName);
            _local_3.OpenLevel = int(_local_4.@OpenLevel);
            _local_3.StopLevel = int(_local_4.@StopLevel);
            _local_3.BuildLevels = String(_local_4.@BuildLevels);
            _local_3.Weight = int(_local_4.@Weight);
            _local_3.ItemTempIds = String(_local_4.@ItemTempIds);
            _local_3.ItemAccess1 = String(_local_4.@ItemAccess1);
            _local_3.ItemAccess2 = String(_local_4.@ItemAccess2);
            _local_3.ItemAccess3 = String(_local_4.@ItemAccess3);
            _local_3.ItemAccess4 = String(_local_4.@ItemAccess4);
            _local_3.ItemAccess5 = String(_local_4.@ItemAccess5);
            _local_3.ModuleAccess = int(_local_4.@ModuleAccess);
            _local_3.Desc = String(_local_4.@Desc);
            _local_3.activeTime = String(_local_4.@activeTime);
            _local_2.add(_local_3.MainType + "_" + _local_3.SonsType + "_" + _local_3.ItemType, _local_3);
         }
         return _local_2;
      }
      
      public function BibleTempAnalyzer()
      {
         super();
      }
   }
}

package bible
{
   import bagAndInfo.BagAndInfoManager;
   import bible.data.BibleGetBackInfo;
   import bible.data.BibleModel;
   import bible.data.BibleTempAnalyzer;
   import braveDoor.BraveDoorManager;
   import com.pickgliss.ui.LayerManager;
   import com.pickgliss.utils.ClassUtils;
   import ddt.bagStore.BagStore;
   import ddt.data.quest.QuestInfo;
   import ddt.events.PkgEvent;
   import ddt.loader.LoaderCreate;
   import ddt.manager.LanguageMgr;
   import ddt.manager.PlayerManager;
   import ddt.manager.SocketManager;
   import ddt.utils.AssetModuleLoader;
   import ddtBuried.BuriedManager;
   import elf.ElfManager;
   import explorerManual.ExplorerManualManager;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import guardCore.GuardCoreManager;
   import hall.HallStateGotoManager;
   import hallIcon.HallIconManager;
   import horse.HorseManager;
   import petsSystem.PetsManager;
   import quest.TaskManager;
   import road7th.comm.PackageIn;
   import vip.VipController;
   import worldboss.WorldBossManager;
   
   public class BibleManager extends EventDispatcher
   {
      
      private static var _ins:BibleManager;
      
      public var model:BibleModel;
      
      public function BibleManager()
      {
         super();
      }
      
      public static function get ins() : BibleManager
      {
         if(_ins == null)
         {
            _ins = new BibleManager();
         }
         return _ins;
      }
      
      public function setup() : void
      {
         this.model = new BibleModel();
         this.addEvents();
      }
      
      private function addEvents() : void
      {
         SocketManager.Instance.addEventListener(PkgEvent.format(652),this.__onBibleData);
      }
      
      private function __onBibleData(_arg_1:PkgEvent) : void
      {
         var _local_2:PackageIn = _arg_1.pkg;
         this.model.cardLevel = _local_2.readInt();
         this.model.exploreLevel = _local_2.readInt();
         var _local_3:int = _local_2.readInt();
         this.model.initData();
      }
      
      public function show() : void
      {
         this.loadRes();
      }
      
      public function loadRes() : void
      {
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatTexpExpLoader());
         AssetModuleLoader.addRequestLoader(LoaderCreate.Instance.creatBibleLoader());
         AssetModuleLoader.addModelLoader("bible",5);
         AssetModuleLoader.startCodeLoader(this.getData);
      }
      
      private function getData() : void
      {
         SocketManager.Instance.out.getHomeTempleLevel();
         SocketManager.Instance.out.sendBibleData();
      }
      
      private function showView() : void
      {
         var _local_1:* = ClassUtils.CreatInstance("bible.view.BibleMain");
         LayerManager.Instance.addToLayer(_local_1,LayerManager.GAME_DYNAMIC_LAYER,true,LayerManager.BLCAK_BLOCKGOUND);
      }
   }
}

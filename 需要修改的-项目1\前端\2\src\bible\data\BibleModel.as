package bible.data
{
   import AvatarCollection.AvatarCollectionManager;
   import beadSystem.beadSystemManager;
   import com.pickgliss.utils.ObjectUtils;
   import ddt.data.BagInfo;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.data.player.SelfInfo;
   import ddt.manager.FineSuitManager;
   import ddt.manager.PlayerManager;
   import ddt.manager.TimeManager;
   import flash.utils.Dictionary;
   import gemstone.GemstoneManager;
   import homeTemple.HomeTempleManager;
   import horse.HorseManager;
   import horse.data.HorsePicCherishVo;
   import horse.data.HorseSkillExpVo;
   import horse.data.HorseSkillGetVo;
   import magicStone.MagicStoneManager;
   import mark.MarkMgr;
   import petsSystem.PetsManager;
   import petsSystem.data.PetInfo;
   import road7th.utils.DateUtils;
   import store.FineEvolutionManager;
   import storefineseal.StoreFineSealManager;
   import storefineseal.data.StoreFineSealInfo;
   import texpSystem.controller.TexpManager;
   import totem.TotemManager;
   
   public class BibleModel
   {
      
      public var tempInfoArr:Array;
      
      public var strongDic:Dictionary;
      
      public var todayTodoDic:Dictionary;
      
      private var rateArr:Array = [2,1,0.5,0];
      
      public var getBackArr:Array;
      
      public var isHide:Boolean = true;
      
      public var cardLevel:int;
      
      public var exploreLevel:int;
      
      public function BibleModel()
      {
         super();
         this.strongDic = new Dictionary();
         this.strongDic[1] = [];
         this.strongDic[2] = [];
         this.strongDic[3] = [];
         this.strongDic[4] = [];
         this.todayTodoDic = new Dictionary();
         this.todayTodoDic[1] = [];
         this.todayTodoDic[2] = [];
         this.todayTodoDic[3] = [];
         this.todayTodoDic[4] = [];
      }
      
      public function initData() : void
      {
         var _local_1:Number = NaN;
         var _local_8:int = 0;
         var _local_6:int = 0;
         var _local_2:StoreFineSealInfo = null;
         var _local_3:* = null;
         var _local_7:* = null;
         var _local_5:* = null;
         this.clear(this.strongDic);
         this.clear(this.todayTodoDic);
         var _local_4:SelfInfo = PlayerManager.Instance.Self;
         for(_local_8 = 0; _local_8 < this.tempInfoArr.length; )
         {
            _local_3 = this.tempInfoArr[_local_8];
            if(_local_3.MainType != 3)
            {
               _local_7 = new BibleTempInfo();
               ObjectUtils.copyProperties(_local_7,_local_3);
               if(_local_7.MainType == 1)
               {
                  if(!this.isLevel(_local_7))
                  {
                     _local_7.Weight = -100;
                     _local_7.isOpenLevel = false;
                     this.strongDic[_local_7.SonsType].push(_local_7);
                  }
                  else
                  {
                     _local_7.isOpenLevel = true;
                     _local_1 = this.getWeight(_local_7);
                     _local_7.Weight = _local_1;
                     this.strongDic[_local_7.SonsType].push(_local_7);
                  }
               }
               else if(_local_7.MainType == 2)
               {
                  if(!this.isLevel(_local_7))
                  {
                     _local_7.Weight = -100;
                     _local_7.isOpenLevel = false;
                     this.todayTodoDic[_local_7.SonsType].push(_local_7);
                  }
                  else
                  {
                     _local_7.isOpenLevel = true;
                     if(!this.isTime(_local_7))
                     {
                        _local_7.Weight = -100;
                        _local_7.isOpenTime = false;
                        this.todayTodoDic[_local_7.SonsType].push(_local_7);
                     }
                     else
                     {
                        _local_7.isOpenTime = true;
                        _local_1 = this.getWeight(_local_7);
                        _local_7.Weight = _local_1;
                        this.todayTodoDic[_local_7.SonsType].push(_local_7);
                     }
                  }
               }
            }
            _local_8++;
         }
         this.sortDic(this.strongDic);
         this.sortDic(this.todayTodoDic);
      }
      
      private function clear(_arg_1:Dictionary) : void
      {
         var _local_2:* = null;
         for(_local_2 in _arg_1)
         {
            _arg_1[_local_2] = [];
         }
      }
      
      private function isLevel(_arg_1:BibleTempInfo) : Boolean
      {
         var _local_2:SelfInfo = PlayerManager.Instance.Self;
         return _local_2.Grade >= _arg_1.OpenLevel && _local_2.Grade <= _arg_1.StopLevel;
      }
      
      private function isTime(_arg_1:BibleTempInfo) : Boolean
      {
         if(_arg_1.activeTime == "")
         {
            return true;
         }
         var _local_2:Array = _arg_1.activeTime.split(",");
         var _local_3:int = int(_local_2[0]);
         var _local_4:int = int(_local_2[1]);
         var _local_5:Date = new Date();
         var _local_6:int = _local_5.getHours();
         return _local_6 >= _local_3 && _local_6 < _local_4;
      }
      
      private function getWeight(_arg_1:BibleTempInfo) : Number
      {
         // 计算权重的逻辑
         return Math.random() * 100;
      }
      
      private function sortDic(_arg_1:Dictionary) : void
      {
         var _local_2:* = null;
         for(_local_2 in _arg_1)
         {
            _arg_1[_local_2].sortOn("Weight", Array.NUMERIC | Array.DESCENDING);
         }
      }
   }
}

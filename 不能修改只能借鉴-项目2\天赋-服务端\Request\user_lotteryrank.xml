<Result value="true" message="Success!">
  <Item ID="1" TemplateID="70244" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="12" Hole1="311429" Hole2="313411" Hole3="313411" Hole4="313411" Hole5="313411" Hole6="313411" Hole5Exp="10000" Hole5Level="4" Hole6Exp="10000" Hole6Level="4" />
  <Item ID="2" TemplateID="9623" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="3" TemplateID="9623" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="4" TemplateID="9624" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="5" TemplateID="9624" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="6" TemplateID="9624" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="7" TemplateID="9624" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="8" TemplateID="8614" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="9" TemplateID="8614" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="10" TemplateID="8614" Count="1" ValidDate="23" IsBinds="true" IsUsed="true" AttackCompose="50" DefendCompose="50" AgilityCompose="50" LuckCompose="50" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="101" TemplateID="11903" Count="1" ValidDate="0" IsBinds="true" IsUsed="false" AttackCompose="0" DefendCompose="0" AgilityCompose="0" LuckCompose="0" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="102" TemplateID="11902" Count="1" ValidDate="0" IsBinds="true" IsUsed="false" AttackCompose="0" DefendCompose="0" AgilityCompose="0" LuckCompose="0" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="103" TemplateID="11901" Count="1" ValidDate="0" IsBinds="true" IsUsed="false" AttackCompose="0" DefendCompose="0" AgilityCompose="0" LuckCompose="0" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
  <Item ID="104" TemplateID="11428" Count="1" ValidDate="0" IsBinds="true" IsUsed="false" AttackCompose="0" DefendCompose="0" AgilityCompose="0" LuckCompose="0" StrengthenLevel="0" Hole1="-1" Hole2="-1" Hole3="-1" Hole4="-1" Hole5="-1" Hole6="-1" Hole5Exp="0" Hole5Level="0" Hole6Exp="0" Hole6Level="0" />
</Result>
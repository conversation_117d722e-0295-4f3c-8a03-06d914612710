package bible.data
{
   public class BibleTempInfo
   {
      
      public var MainType:int;
      
      public var SonsType:int;
      
      public var ItemType:int;
      
      public var ItemName:String;
      
      public var OpenLevel:int;
      
      public var StopLevel:int;
      
      public var BuildLevels:String;
      
      public var Weight:int;
      
      public var ItemTempIds:String;
      
      public var ItemAccess1:String;
      
      public var ItemAccess2:String;
      
      public var ItemAccess3:String;
      
      public var ItemAccess4:String;
      
      public var ItemAccess5:String;
      
      public var ModuleAccess:int;
      
      public var Desc:String;
      
      public var isOpenLevel:Boolean = true;
      
      public var upType:int;
      
      public var level:Number;
      
      public var param1:String;
      
      public var param2:String;
      
      public var activeTime:String;
      
      public var isOpenTime:Boolean = true;
      
      public var state:int;
      
      public var isToday:Boolean = true;
      
      public function BibleTempInfo()
      {
         super();
      }
   }
}

using System;
using Game.Base.Packets;

namespace Game.Server.Packets.Client
{
	// Token: 0x020002BC RID: 700
	[PacketHandler(652, "弹王宝典")]
	public class BibleHandler : IPacketHandler
	{
		// Token: 0x06000D08 RID: 3336 RVA: 0x0006F844 File Offset: 0x0006DA44
		public int HandlePacket(GameClient client, GSPacketIn packet)
		{
			GSPacketIn gspacketIn = new GSPacketIn(652);
			gspacketIn.WriteInt(client.Player.PlayerCharacter.CardInfo.CardLevel);
			gspacketIn.WriteInt(client.Player.PlayerCharacter.explorerManualInfo.manualLevel);
			gspacketIn.WriteInt(0);
			client.Out.SendTCP(gspacketIn);
			client.Player.EquipBag.UpdatePlayerProperties();
			return 0;
		}
	}
}
